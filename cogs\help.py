import discord
from discord.ext import commands

COMMANDS_PER_PAGE = 15

class Help2View(discord.ui.View):
    def __init__(self, bot, ctx):
        super().__init__(timeout=120)
        self.bot = bot
        self.ctx = ctx

        # Get all cogs with visible commands, including subcommands
        self.all_cogs = []
        self.all_cog_commands = []
        for cog_name in sorted(bot.cogs):
            cog = bot.get_cog(cog_name)
            visible = []
            for cmd in cog.get_commands():
                if not cmd.hidden:
                    visible.append(cmd)
                    # Add subcommands if any
                    if hasattr(cmd, "commands"):
                        for subcmd in cmd.commands:
                            if not subcmd.hidden:
                                visible.append(subcmd)
            if visible:
                self.all_cogs.append(cog_name)
                self.all_cog_commands.append(visible)

        self.category_page = 0  # For category pagination
        self.categories_per_page = 25  # Discord's limit
        self.selected_cog_index = None  # Track which cog is selected
        self.command_page = 0  # For command pagination within a cog
        self.showing_main = True  # Track if we're showing the main menu
        
        self.update_view()

    def update_view(self):
        self.clear_items()
        if self.showing_main:
            self.add_main_menu_items()
        else:
            self.add_command_menu_items()

    def add_main_menu_items(self):
        start = self.category_page * self.categories_per_page
        end = start + self.categories_per_page
        current_cogs = self.all_cogs[start:end]

        if current_cogs:
            options = [
                discord.SelectOption(
                    label=cog_name,
                    value=str(start + i),
                    description=f"{len(self.all_cog_commands[start + i])} commands"
                )
                for i, cog_name in enumerate(current_cogs)
            ]

            select = discord.ui.Select(
                placeholder="Select a Category",
                options=options,
                min_values=1,
                max_values=1
            )
            select.callback = self.select_callback
            self.add_item(select)

        max_category_pages = (len(self.all_cogs) - 1) // self.categories_per_page + 1
        if max_category_pages > 1:
            prev_cat_btn = discord.ui.Button(
                label="Previous Categories",
                style=discord.ButtonStyle.secondary,
                disabled=self.category_page == 0
            )
            prev_cat_btn.callback = self.prev_categories
            self.add_item(prev_cat_btn)

            next_cat_btn = discord.ui.Button(
                label="Next Categories",
                style=discord.ButtonStyle.secondary,
                disabled=self.category_page >= max_category_pages - 1
            )
            next_cat_btn.callback = self.next_categories
            self.add_item(next_cat_btn)

    def add_command_menu_items(self):
        back_btn = discord.ui.Button(
            label="Back to Categories", 
            style=discord.ButtonStyle.secondary
        )
        back_btn.callback = self.back_to_main
        self.add_item(back_btn)
        
        if self.selected_cog_index is not None:
            commands = self.all_cog_commands[self.selected_cog_index]
            max_pages = (len(commands) - 1) // COMMANDS_PER_PAGE + 1
            
            if max_pages > 1:
                prev_btn = discord.ui.Button(
                    emoji="<:pleft:1397934731248664597>", 
                    style=discord.ButtonStyle.secondary,
                    disabled=self.command_page == 0
                )
                prev_btn.callback = self.prev_command_page
                self.add_item(prev_btn)
                
                next_btn = discord.ui.Button(
                    emoji="<:pright:1397934811301150740>", 
                    style=discord.ButtonStyle.secondary,
                    disabled=self.command_page >= max_pages - 1
                )
                next_btn.callback = self.next_command_page
                self.add_item(next_btn)

    async def select_callback(self, interaction: discord.Interaction):
        select = None
        for item in self.children:
            if isinstance(item, discord.ui.Select) and item.values:
                select = item
                break
        if select is None:
            await interaction.response.defer()
            return

        self.selected_cog_index = int(select.values[0])
        self.command_page = 0
        self.showing_main = False
        self.update_view()
        await interaction.response.edit_message(embed=self.get_embed(), view=self)

    async def back_to_main(self, interaction: discord.Interaction):
        self.showing_main = True
        self.selected_cog_index = None
        self.command_page = 0
        self.update_view()
        await interaction.response.edit_message(embed=self.get_embed(), view=self)

    async def prev_categories(self, interaction: discord.Interaction):
        if self.category_page > 0:
            self.category_page -= 1
            self.update_view()
            await interaction.response.edit_message(embed=self.get_embed(), view=self)
        else:
            await interaction.response.defer()

    async def next_categories(self, interaction: discord.Interaction):
        max_cat_page = (len(self.all_cogs) - 1) // self.categories_per_page
        if self.category_page < max_cat_page:
            self.category_page += 1
            self.update_view()
            await interaction.response.edit_message(embed=self.get_embed(), view=self)
        else:
            await interaction.response.defer()

    async def prev_command_page(self, interaction: discord.Interaction):
        if self.command_page > 0:
            self.command_page -= 1
            self.update_view()
            await interaction.response.edit_message(embed=self.get_embed(), view=self)
        else:
            await interaction.response.defer()

    async def next_command_page(self, interaction: discord.Interaction):
        if self.selected_cog_index is not None:
            commands = self.all_cog_commands[self.selected_cog_index]
            max_pages = (len(commands) - 1) // COMMANDS_PER_PAGE
            if self.command_page < max_pages:
                self.command_page += 1
                self.update_view()
                await interaction.response.edit_message(embed=self.get_embed(), view=self)
            else:
                await interaction.response.defer()

    def get_embed(self):
        if self.showing_main:
            embed = discord.Embed(
                description="> ``[ ] = optional``\n > ``< > = required``\n\n"
                            "> [**Invite**](https://discord.com/oauth2/authorize?client_id=1393143499678814208&scope=bot+applications.commands&permissions=956689622)\n"
                            "> [**Support**](https://discord.gg/codexdev)",
                color=self.bot.mode.EMBED_COLOR,
            )
            embed.set_thumbnail(url=self.bot.user.display_avatar.url)
            embed.set_author(name=self.bot.user.display_name, icon_url=self.bot.user.display_avatar.url)
            max_category_pages = (len(self.all_cogs) - 1) // self.categories_per_page + 1
            if max_category_pages > 1:
                embed.set_footer(text=f"Category Page {self.category_page + 1}/{max_category_pages} • Total Categories: {len(self.all_cogs)}")
            else:
                embed.set_footer(text=f"Total Categories: {len(self.all_cogs)}")
            return embed
        else:
            if self.selected_cog_index is None or not self.all_cogs:
                embed = discord.Embed(
                    title="No Commands",
                    description="No commands found.",
                    color=self.bot.mode.EMBED_COLOR,
                )
                return embed
                
            cog_name = self.all_cogs[self.selected_cog_index]
            commands = self.all_cog_commands[self.selected_cog_index]
            start = self.command_page * COMMANDS_PER_PAGE
            end = start + COMMANDS_PER_PAGE
            page_commands = commands[start:end]
            max_pages = (len(commands) - 1) // COMMANDS_PER_PAGE + 1
            cmd_list = "\n".join([
                f"`{cmd.name}`: {cmd.brief or 'No description.'}" 
                for cmd in page_commands
            ])
            embed = discord.Embed(
                title=f"{cog_name} Commands",
                description=cmd_list if cmd_list else "No commands found.",
                color=self.bot.mode.EMBED_COLOR,
            )
            embed.set_thumbnail(url=self.bot.user.display_avatar.url)
            embed.set_footer(text=f"Page {self.command_page + 1}/{max_pages} • {len(commands)} total commands")
            return embed

    async def on_timeout(self):
        for item in self.children:
            item.disabled = True
        try:
            embed = self.get_embed()
            embed.set_footer(text="This help menu has timed out.")
            await self.message.edit(embed=embed, view=self)
        except:
            pass

class Help2(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    @commands.command(name="help", brief="Interactive help menu grouped by category.")
    async def help(self, ctx, *, command_name: str = None):
        if not command_name:
            view = Help2View(self.bot, ctx)
            embed = view.get_embed()
            message = await ctx.send(embed=embed, view=view)
            view.message = message  # Store message reference for timeout handling
            return

        # Try to find the command by name or alias
        command = self.bot.get_command(command_name.lower())
        if not command or command.hidden:
            await ctx.send(f"No command named `{command_name}` found.")
            return

        usage = f"{ctx.clean_prefix}{command.qualified_name} {command.signature}" if hasattr(command, "signature") else command.name
        embed = discord.Embed(
            title=f"Command: `{command.qualified_name}`",
            description=f"> **Usage:** `{usage}`\n> **Description:** {command.help or command.brief or 'No description.'}",
            color=self.bot.mode.EMBED_COLOR,
        )
        await ctx.send(embed=embed)

async def setup(bot):
    bot.remove_command("help")  # This disables the default help command
    await bot.add_cog(Help2(bot))